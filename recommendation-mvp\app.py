import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime
import os
import json
import hashlib
import threading
import time
from dataclasses import dataclass, asdict # Import asdict for easier serialization
from flask import Flask, Blueprint, jsonify, render_template, request, send_from_directory
from scipy.stats import pearsonr, spearmanr
from scipy.spatial.distance import cosine
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
from collections import defaultdict, Counter
import warnings
warnings.filterwarnings('ignore')
# Assuming config.py exists and defines Config class
# from config import Config

# --- Configuration (Placeholder if config.py is not available) ---
class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'you-will-never-guess'
    # Add other configurations if needed

# --- Logging Setup ---
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s [%(levelname)s] %(name)s: %(message)s", # Improved format
    datefmt="%Y-%m-%d %H:%M:%S"
)
logger = logging.getLogger(__name__)

# --- Data Models ---
@dataclass
class ServerData:
    id: str
    environment: str
    status: str
    last_update: Optional[str] # Store as ISO string for JSON compatibility
    cluster: str
    products: List[str]
    operations: List[str]
    l_levels: Optional[Dict[str, str]] = None # Dictionary to store all L-level values

@dataclass
class AppData:
    name: str
    priority_score: float
    servers: List[ServerData]
    cluster_distribution: Dict[str, int]
    status_summary: Dict[str, int]
    most_common_product: Optional[str]
    most_common_operation: Optional[str]
    # Add l_level derived from servers if needed for frontend card display
    l_level: Optional[str] = None # This field should contain 'L4', 'L5', etc. or None/null

    @property
    def server_count(self) -> int:
        return len(self.servers)

@dataclass
class OwnerData:
    owner: str
    total_apps: int
    total_servers: int
    environment_distribution: Dict[str, int]
    apps: List[AppData] # Should contain AppData objects

@dataclass
class RemediationStats:
    total_items: int
    remediated_count: int
    pending_count: int
    success_rate: float
    failed_count: int
    unknown_count: int

@dataclass
class ProductRecommendation:
    target_product: str
    recommended_product: str
    correlation_score: float
    confidence_level: float
    reasoning: str
    statistical_significance: float
    co_occurrence_count: int
    success_rate_improvement: float

@dataclass
class OwnerProductAffinity:
    owner: str
    product: str
    affinity_score: float
    success_rate: float
    execution_count: int
    expertise_level: str
    recommended_products: List[ProductRecommendation]

@dataclass
class CrossProductRecommendation:
    primary_product: str
    secondary_product: str
    correlation_coefficient: float
    confidence_score: float
    environment_pattern: str
    cluster_pattern: str
    success_rate_combined: float
    reasoning: str

@dataclass
class OwnerBehaviorInsight:
    owner: str
    consistency_score: float
    high_performing_products: List[str]
    low_performing_products: List[str]
    neglected_products: List[str]
    recommended_actions: List[str]
    cluster_pattern: str
    risk_level: str  # "high", "medium", "low"

@dataclass
class ProductClusterInsight:
    cluster_id: str
    dominant_products: List[str]
    success_pattern: str
    recommended_product_combinations: List[Dict[str, Any]]
    outlier_owners: List[str]

@dataclass
class IntelligentRecommendation:
    type: str  # "owner_behavior", "product_cluster", "cross_remediation"
    priority: str  # "critical", "high", "medium", "low"
    title: str
    description: str
    affected_owners: List[str]
    affected_products: List[str]
    confidence_score: float
    potential_impact: str
    suggested_actions: List[str]

@dataclass
class ProductLevelRecommendations:
    product_to_product: List[ProductRecommendation]
    owner_product_affinities: List[OwnerProductAffinity]
    cross_product_recommendations: List[CrossProductRecommendation]
    correlation_matrix: Dict[str, Dict[str, float]]
    insights: List[str]
    # New intelligent insights
    owner_behavior_insights: List[OwnerBehaviorInsight]
    product_cluster_insights: List[ProductClusterInsight]
    intelligent_recommendations: List[IntelligentRecommendation]

@dataclass
class SummaryData:
    total_owners: int
    total_apps: int # Added total apps to summary
    total_servers: int # Added total servers to summary
    environment_distribution: Dict[str, int]
    cluster_distribution: Dict[str, int]
    most_common_products: Dict[str, int]
    operation_types: Dict[str, int]
    remediation_stats: RemediationStats
    top_5_applications: List[Dict[str, Any]]

@dataclass
class RecommendationReport:
    summary: SummaryData
    recommendations: Dict[str, OwnerData] # Dict mapping owner name to OwnerData
    metadata: Dict[str, any]

# --- Product Recommendation Cache ---
class ProductRecommendationCache:
    def __init__(self):
        self.cache = {}
        self.cache_timestamps = {}
        self.cache_lock = threading.Lock()
        self.cache_ttl = 3600  # 1 hour TTL
        self.background_update_thread = None
        self.stop_background_updates = False

    def get_cache_key(self, df_hash: str, filters: Dict[str, Any] = None) -> str:
        """Generate a cache key based on data hash and filters."""
        filter_str = json.dumps(filters or {}, sort_keys=True)
        return hashlib.md5(f"{df_hash}_{filter_str}".encode()).hexdigest()

    def get_data_hash(self, df: pd.DataFrame) -> str:
        """Generate a hash for the DataFrame to detect changes."""
        return hashlib.md5(str(df.shape).encode() + str(df.columns.tolist()).encode()).hexdigest()

    def is_cache_valid(self, cache_key: str) -> bool:
        """Check if cache entry is still valid."""
        if cache_key not in self.cache_timestamps:
            return False
        return time.time() - self.cache_timestamps[cache_key] < self.cache_ttl

    def get_cached_recommendations(self, cache_key: str) -> Optional[ProductLevelRecommendations]:
        """Get cached recommendations if valid."""
        with self.cache_lock:
            if cache_key in self.cache and self.is_cache_valid(cache_key):
                return self.cache[cache_key]
        return None

    def cache_recommendations(self, cache_key: str, recommendations: ProductLevelRecommendations):
        """Cache recommendations with timestamp."""
        with self.cache_lock:
            self.cache[cache_key] = recommendations
            self.cache_timestamps[cache_key] = time.time()

    def clear_expired_cache(self):
        """Remove expired cache entries."""
        with self.cache_lock:
            current_time = time.time()
            expired_keys = [
                key for key, timestamp in self.cache_timestamps.items()
                if current_time - timestamp >= self.cache_ttl
            ]
            for key in expired_keys:
                self.cache.pop(key, None)
                self.cache_timestamps.pop(key, None)

    def start_background_cache_cleanup(self):
        """Start background thread for cache cleanup."""
        if self.background_update_thread is None or not self.background_update_thread.is_alive():
            self.stop_background_updates = False
            self.background_update_thread = threading.Thread(target=self._background_cache_cleanup, daemon=True)
            self.background_update_thread.start()

    def _background_cache_cleanup(self):
        """Background process to clean expired cache entries."""
        while not self.stop_background_updates:
            try:
                self.clear_expired_cache()
                time.sleep(300)  # Clean every 5 minutes
            except Exception as e:
                logger.error(f"Error in background cache cleanup: {e}")
                time.sleep(60)  # Wait 1 minute before retrying

    def stop_background_cleanup(self):
        """Stop background cache cleanup."""
        self.stop_background_updates = True

# --- Recommendation Service ---
class RecommendationService:
    def __init__(self):
        # Priority weights can be adjusted
        self.priority_weights = {
            "cluster_2": 2.0,
            "cluster_1": 1.5,
            "cluster_0": 1.0,
            "Production": 2.0,
            "Non-Production": 1.0,
            "path_frequent": 1.5,
            "success_history": 1.2
        }
        # Define expected L-level columns for consistency
        self.l_level_columns = [
            "CMDB_L2", "L2_Name", "CMDB_L3_NAME", "CMDB_L4_NAME",
            "CMDB_L5_NAME", "CMDB_L6_NAME", "CMDB_L7_NAME", "CMDB_L8_NAME"
        ]
        self.app_levels_column = "APP_LEVELS" # Specific column for combined app levels

        # Initialize caching system
        self.product_cache = ProductRecommendationCache()
        self.precomputed_data = {}
        self.last_data_hash = None

    def _precompute_base_data(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Pre-compute base data structures for fast recommendations."""
        logger.info("Pre-computing base data for product recommendations...")

        # Product statistics
        product_stats = {}
        for product in df["PRODUCT"].dropna().unique():
            product_data = df[df["PRODUCT"] == product]
            success_count = len(product_data[product_data["STATUS"] == "SUCCESS"])
            total_count = len(product_data)

            product_stats[str(product)] = {
                "success_rate": success_count / max(1, total_count),
                "total_executions": total_count,
                "owners": set(product_data["APPOWNER"].dropna()),
                "environments": set(product_data["ENVIRONMENT"].dropna()),
                "clusters": set(product_data["CLUSTER"].dropna()),
                "applications": set(product_data["APP_NAME"].dropna())
            }

        # Owner-product relationships
        owner_product_stats = {}
        for owner in df["APPOWNER"].dropna().unique():
            owner_data = df[df["APPOWNER"] == owner]
            owner_products = {}

            for product in owner_data["PRODUCT"].dropna().unique():
                product_data = owner_data[owner_data["PRODUCT"] == product]
                success_count = len(product_data[product_data["STATUS"] == "SUCCESS"])
                total_count = len(product_data)

                owner_products[str(product)] = {
                    "success_rate": success_count / max(1, total_count),
                    "execution_count": total_count,
                    "success_count": success_count
                }

            owner_product_stats[str(owner)] = owner_products

        # Pre-compute simple correlation matrix
        correlation_matrix = self._fast_correlation_matrix(df, product_stats)

        return {
            "product_stats": product_stats,
            "owner_product_stats": owner_product_stats,
            "correlation_matrix": correlation_matrix,
            "total_products": len(product_stats),
            "total_owners": len(owner_product_stats)
        }

    def _fast_correlation_matrix(self, df: pd.DataFrame, product_stats: Dict[str, Any]) -> Dict[str, Dict[str, float]]:
        """Fast correlation matrix calculation using pre-computed stats."""
        products = list(product_stats.keys())
        correlation_matrix = {}

        for product1 in products:
            correlation_matrix[product1] = {}
            stats1 = product_stats[product1]

            for product2 in products:
                if product1 == product2:
                    correlation_matrix[product1][product2] = 1.0
                    continue

                stats2 = product_stats[product2]

                # Fast correlation based on pre-computed sets
                owner_overlap = len(stats1["owners"] & stats2["owners"])
                total_owners = len(stats1["owners"] | stats2["owners"])
                owner_similarity = owner_overlap / max(1, total_owners)

                env_overlap = len(stats1["environments"] & stats2["environments"])
                total_envs = len(stats1["environments"] | stats2["environments"])
                env_similarity = env_overlap / max(1, total_envs)

                success_similarity = 1 - abs(stats1["success_rate"] - stats2["success_rate"])

                # Combined correlation
                correlation = (0.5 * owner_similarity + 0.3 * success_similarity + 0.2 * env_similarity)
                correlation_matrix[product1][product2] = round(correlation, 3)

        return correlation_matrix

    def _get_cached_or_compute_recommendations(self, df: pd.DataFrame, filters: Dict[str, Any] = None) -> ProductLevelRecommendations:
        """Get recommendations from cache or compute if needed."""
        # Generate cache key
        data_hash = self.product_cache.get_data_hash(df)
        cache_key = self.product_cache.get_cache_key(data_hash, filters)

        # Try to get from cache
        cached_recommendations = self.product_cache.get_cached_recommendations(cache_key)
        if cached_recommendations:
            logger.info("Returning cached product recommendations")
            return cached_recommendations

        # Check if we need to update precomputed data
        if self.last_data_hash != data_hash:
            logger.info("Data changed, updating precomputed base data")
            self.precomputed_data = self._precompute_base_data(df)
            self.last_data_hash = data_hash

        # Generate recommendations using precomputed data
        logger.info("Computing new product recommendations using precomputed data")
        recommendations = self._fast_generate_product_recommendations(df, filters)

        # Cache the results
        self.product_cache.cache_recommendations(cache_key, recommendations)

        return recommendations

    def _fast_generate_product_recommendations(self, df: pd.DataFrame, filters: Dict[str, Any] = None) -> ProductLevelRecommendations:
        """Fast generation of product recommendations using precomputed data."""
        if not self.precomputed_data:
            # Fallback to full computation if no precomputed data
            return self._generate_product_level_recommendations(df)

        # Use precomputed data for fast recommendations
        product_stats = self.precomputed_data["product_stats"]
        owner_stats = self.precomputed_data["owner_product_stats"]
        correlation_matrix = self.precomputed_data["correlation_matrix"]

        # Fast product-to-product recommendations
        product_to_product = self._fast_product_to_product_recommendations(product_stats, correlation_matrix)

        # Fast owner-product affinities
        owner_product_affinities = self._fast_owner_product_affinities(owner_stats, product_stats)

        # Fast cross-product recommendations
        cross_product_recommendations = self._fast_cross_product_recommendations(df, product_stats, correlation_matrix)

        # Generate insights
        insights = self._generate_fast_insights(product_stats, owner_stats, correlation_matrix)

        # Generate intelligent insights
        owner_behavior_insights = self._analyze_owner_behavior_patterns(df, owner_stats, product_stats)
        product_cluster_insights = self._analyze_product_cluster_patterns(df, product_stats)
        intelligent_recommendations = self._generate_intelligent_recommendations(df, owner_behavior_insights, product_cluster_insights)

        return ProductLevelRecommendations(
            product_to_product=product_to_product,
            owner_product_affinities=owner_product_affinities,
            cross_product_recommendations=cross_product_recommendations,
            correlation_matrix=correlation_matrix,
            insights=insights,
            owner_behavior_insights=owner_behavior_insights,
            product_cluster_insights=product_cluster_insights,
            intelligent_recommendations=intelligent_recommendations
        )

    def _fast_product_to_product_recommendations(self, product_stats: Dict[str, Any], correlation_matrix: Dict[str, Dict[str, float]]) -> List[ProductRecommendation]:
        """Fast product-to-product recommendations using precomputed data."""
        recommendations = []

        for target_product, correlations in correlation_matrix.items():
            # Sort by correlation score, excluding self-correlation
            sorted_correlations = sorted(
                [(prod, score) for prod, score in correlations.items() if prod != target_product and score > 0.3],
                key=lambda x: x[1], reverse=True
            )

            # Take top 3 recommendations per product
            for recommended_product, correlation_score in sorted_correlations[:3]:
                target_stats = product_stats[target_product]
                recommended_stats = product_stats[recommended_product]

                # Calculate metrics using precomputed stats
                co_occurrence_count = len(target_stats["owners"] & recommended_stats["owners"])
                statistical_significance = min(1.0, min(target_stats["total_executions"], recommended_stats["total_executions"]) / 30.0)
                success_rate_improvement = recommended_stats["success_rate"] - target_stats["success_rate"]
                confidence_level = correlation_score * statistical_significance

                # Generate reasoning
                reasoning = f"Products frequently managed together by {co_occurrence_count} common owners. "
                if success_rate_improvement > 0:
                    reasoning += f"Switching could improve success rate by {success_rate_improvement*100:.1f}%."
                else:
                    reasoning += f"Both products show similar success patterns."

                recommendations.append(ProductRecommendation(
                    target_product=target_product,
                    recommended_product=recommended_product,
                    correlation_score=correlation_score,
                    confidence_level=confidence_level,
                    reasoning=reasoning,
                    statistical_significance=statistical_significance,
                    co_occurrence_count=co_occurrence_count,
                    success_rate_improvement=success_rate_improvement
                ))

        # Sort by confidence level and return top recommendations
        recommendations.sort(key=lambda x: x.confidence_level, reverse=True)
        return recommendations[:20]

    def _fast_owner_product_affinities(self, owner_stats: Dict[str, Any], product_stats: Dict[str, Any]) -> List[OwnerProductAffinity]:
        """Fast owner-product affinity analysis using precomputed data."""
        affinities = []

        for owner, products in owner_stats.items():
            for product, stats in products.items():
                execution_count = stats["execution_count"]
                success_rate = stats["success_rate"]

                # Calculate affinity score
                frequency_score = min(1.0, execution_count / 20.0)
                affinity_score = (0.6 * success_rate + 0.4 * frequency_score)

                # Determine expertise level
                if success_rate >= 0.8 and execution_count >= 10:
                    expertise_level = "Expert"
                elif success_rate >= 0.6 and execution_count >= 5:
                    expertise_level = "Proficient"
                elif success_rate >= 0.4:
                    expertise_level = "Developing"
                else:
                    expertise_level = "Novice"

                # Generate simple product recommendations (top 2 correlated products)
                recommended_products = []
                if product in self.precomputed_data["correlation_matrix"]:
                    correlations = self.precomputed_data["correlation_matrix"][product]
                    sorted_correlations = sorted(
                        [(prod, score) for prod, score in correlations.items() if prod != product and score > 0.2],
                        key=lambda x: x[1], reverse=True
                    )

                    for recommended_product, correlation_score in sorted_correlations[:2]:
                        # Check if owner has experience with recommended product
                        if recommended_product in products:
                            rec_stats = products[recommended_product]
                            success_improvement = rec_stats["success_rate"] - success_rate
                            reasoning = f"You have {rec_stats['execution_count']} executions with {rec_stats['success_rate']*100:.1f}% success rate."
                        else:
                            rec_product_stats = product_stats.get(recommended_product, {})
                            overall_success = rec_product_stats.get("success_rate", 0)
                            success_improvement = overall_success - success_rate
                            reasoning = f"Similar product with {overall_success*100:.1f}% overall success rate."

                        recommended_products.append(ProductRecommendation(
                            target_product=product,
                            recommended_product=recommended_product,
                            correlation_score=correlation_score,
                            confidence_level=correlation_score * 0.8,
                            reasoning=reasoning,
                            statistical_significance=min(1.0, execution_count / 10.0),
                            co_occurrence_count=len(product_stats[product]["owners"] & product_stats[recommended_product]["owners"]),
                            success_rate_improvement=success_improvement
                        ))

                affinities.append(OwnerProductAffinity(
                    owner=owner,
                    product=product,
                    affinity_score=round(affinity_score, 3),
                    success_rate=round(success_rate, 3),
                    execution_count=execution_count,
                    expertise_level=expertise_level,
                    recommended_products=recommended_products
                ))

        # Sort by affinity score
        affinities.sort(key=lambda x: x.affinity_score, reverse=True)
        return affinities

    def _fast_cross_product_recommendations(self, df: pd.DataFrame, product_stats: Dict[str, Any], correlation_matrix: Dict[str, Dict[str, float]]) -> List[CrossProductRecommendation]:
        """Fast cross-product recommendations using precomputed data."""
        recommendations = []

        for primary_product, correlations in correlation_matrix.items():
            primary_stats = product_stats[primary_product]

            # Find highly correlated products
            sorted_correlations = sorted(
                [(prod, score) for prod, score in correlations.items() if prod != primary_product and score > 0.4],
                key=lambda x: x[1], reverse=True
            )

            for secondary_product, correlation_coefficient in sorted_correlations[:2]:
                secondary_stats = product_stats[secondary_product]

                # Analyze patterns using precomputed sets
                common_envs = primary_stats["environments"] & secondary_stats["environments"]
                common_clusters = primary_stats["clusters"] & secondary_stats["clusters"]

                env_pattern = f"Both products used in: {', '.join([str(env) for env in list(common_envs)[:3]])}" if common_envs else "Different environments"
                cluster_pattern = f"Common clusters: {', '.join([str(cluster) for cluster in list(common_clusters)[:3]])}" if common_clusters else "Different clusters"

                # Calculate combined success rate
                combined_success_rate = (primary_stats["success_rate"] + secondary_stats["success_rate"]) / 2

                # Calculate confidence score
                env_overlap_score = len(common_envs) / max(1, len(primary_stats["environments"] | secondary_stats["environments"]))
                cluster_overlap_score = len(common_clusters) / max(1, len(primary_stats["clusters"] | secondary_stats["clusters"]))
                confidence_score = correlation_coefficient * (0.5 * env_overlap_score + 0.5 * cluster_overlap_score)

                # Generate reasoning
                reasoning = f"Products show {correlation_coefficient:.1%} correlation. "
                if env_overlap_score > 0.5:
                    reasoning += f"Frequently used together in {', '.join([str(env) for env in list(common_envs)[:2]])} environments. "
                if combined_success_rate > 0.7:
                    reasoning += f"Combined approach shows {combined_success_rate:.1%} success rate."
                else:
                    reasoning += f"Consider sequential implementation for better results."

                recommendations.append(CrossProductRecommendation(
                    primary_product=primary_product,
                    secondary_product=secondary_product,
                    correlation_coefficient=correlation_coefficient,
                    confidence_score=round(confidence_score, 3),
                    environment_pattern=env_pattern,
                    cluster_pattern=cluster_pattern,
                    success_rate_combined=round(combined_success_rate, 3),
                    reasoning=reasoning
                ))

        # Sort by confidence score
        recommendations.sort(key=lambda x: x.confidence_score, reverse=True)
        return recommendations[:15]

    def _generate_fast_insights(self, product_stats: Dict[str, Any], owner_stats: Dict[str, Any], correlation_matrix: Dict[str, Dict[str, float]]) -> List[str]:
        """Generate insights using precomputed data."""
        insights = []

        # Product correlation insights
        if correlation_matrix:
            max_correlation = 0
            best_pair = None
            for p1, correlations in correlation_matrix.items():
                for p2, score in correlations.items():
                    if p1 != p2 and score > max_correlation:
                        max_correlation = score
                        best_pair = (p1, p2)

            if best_pair:
                insights.append(f"Strongest product correlation: {best_pair[0]} and {best_pair[1]} ({max_correlation:.1%})")

        # Owner expertise insights
        expert_count = 0
        total_affinities = 0
        for owner, products in owner_stats.items():
            for product, stats in products.items():
                total_affinities += 1
                if stats["success_rate"] >= 0.8 and stats["execution_count"] >= 10:
                    expert_count += 1

        insights.append(f"Found {expert_count} expert-level owner-product relationships")

        # Product diversity insight
        total_products = len(product_stats)
        total_owners = len(owner_stats)
        avg_products_per_owner = total_affinities / max(1, total_owners)
        insights.append(f"Average {avg_products_per_owner:.1f} products per owner across {total_products} total products")

        return insights

    def _analyze_owner_behavior_patterns(self, df: pd.DataFrame, owner_stats: Dict[str, Any], product_stats: Dict[str, Any]) -> List[OwnerBehaviorInsight]:
        """Analyze owner behavior patterns to identify inconsistencies and opportunities."""
        insights = []

        for owner, stats in owner_stats.items():
            if stats["total_executions"] < 5:  # Skip owners with too few executions
                continue

            owner_data = df[df["APPOWNER"] == owner]

            # Calculate product-level performance for this owner
            product_performance = {}
            for product in stats["products"]:
                product_data = owner_data[owner_data["PRODUCT"] == product]
                if len(product_data) > 0:
                    success_count = len(product_data[product_data["STATUS"].str.contains("SUCCESS", case=False, na=False)])
                    total_count = len(product_data)
                    success_rate = success_count / total_count if total_count > 0 else 0
                    product_performance[product] = {
                        "success_rate": success_rate,
                        "total_executions": total_count,
                        "last_execution": product_data["REQ_CREATE_DATE"].max() if "REQ_CREATE_DATE" in product_data.columns else None
                    }

            # Identify high and low performing products
            high_performing = []
            low_performing = []
            neglected_products = []

            for product, perf in product_performance.items():
                if perf["success_rate"] >= 0.8 and perf["total_executions"] >= 3:
                    high_performing.append(product)
                elif perf["success_rate"] <= 0.4 and perf["total_executions"] >= 2:
                    low_performing.append(product)

            # Find products that are common in the owner's cluster but not used by this owner
            owner_cluster = owner_data["CLUSTER"].mode().iloc[0] if len(owner_data) > 0 and "CLUSTER" in owner_data.columns else None
            if owner_cluster is not None:
                cluster_data = df[df["CLUSTER"] == owner_cluster]
                common_cluster_products = set(cluster_data["PRODUCT"].value_counts().head(10).index)
                owner_products = set(stats["products"])
                neglected_products = list(common_cluster_products - owner_products)

            # Calculate consistency score
            if len(product_performance) > 1:
                success_rates = [perf["success_rate"] for perf in product_performance.values()]
                consistency_score = 1 - (max(success_rates) - min(success_rates))  # Higher is more consistent
            else:
                consistency_score = 1.0

            # Determine risk level
            risk_level = "low"
            if len(low_performing) > len(high_performing):
                risk_level = "high"
            elif len(low_performing) > 0 or consistency_score < 0.6:
                risk_level = "medium"

            # Generate recommendations
            recommended_actions = []
            if len(low_performing) > 0:
                recommended_actions.append(f"Focus on improving success rate for: {', '.join(low_performing[:3])}")
            if len(neglected_products) > 0:
                recommended_actions.append(f"Consider adopting cluster-common products: {', '.join(neglected_products[:3])}")
            if consistency_score < 0.7:
                recommended_actions.append("Review processes to improve consistency across products")

            cluster_pattern = f"Cluster {owner_cluster}" if owner_cluster is not None else "Unknown"

            insights.append(OwnerBehaviorInsight(
                owner=owner,
                consistency_score=round(consistency_score, 3),
                high_performing_products=high_performing,
                low_performing_products=low_performing,
                neglected_products=neglected_products[:5],  # Limit to top 5
                recommended_actions=recommended_actions,
                cluster_pattern=cluster_pattern,
                risk_level=risk_level
            ))

        # Sort by risk level and consistency score
        risk_priority = {"high": 3, "medium": 2, "low": 1}
        insights.sort(key=lambda x: (risk_priority[x.risk_level], -x.consistency_score), reverse=True)

        return insights[:20]  # Return top 20 insights

    def _analyze_product_cluster_patterns(self, df: pd.DataFrame, product_stats: Dict[str, Any]) -> List[ProductClusterInsight]:
        """Analyze product patterns within clusters to identify optimization opportunities."""
        insights = []

        if "CLUSTER" not in df.columns:
            return insights

        clusters = df["CLUSTER"].unique()

        for cluster in clusters:
            cluster_data = df[df["CLUSTER"] == cluster]

            # Get dominant products in this cluster
            product_counts = cluster_data["PRODUCT"].value_counts()
            dominant_products = product_counts.head(5).index.tolist()

            # Analyze success patterns
            cluster_success_rate = len(cluster_data[cluster_data["STATUS"].str.contains("SUCCESS", case=False, na=False)]) / len(cluster_data)

            if cluster_success_rate >= 0.8:
                success_pattern = "High Success"
            elif cluster_success_rate >= 0.6:
                success_pattern = "Moderate Success"
            else:
                success_pattern = "Low Success"

            # Find recommended product combinations
            recommended_combinations = []
            for i, product1 in enumerate(dominant_products[:3]):
                for product2 in dominant_products[i+1:4]:
                    # Check if these products are often used together
                    product1_owners = set(cluster_data[cluster_data["PRODUCT"] == product1]["APPOWNER"])
                    product2_owners = set(cluster_data[cluster_data["PRODUCT"] == product2]["APPOWNER"])
                    overlap = len(product1_owners & product2_owners)

                    if overlap >= 2:  # At least 2 owners use both
                        combined_data = cluster_data[cluster_data["PRODUCT"].isin([product1, product2])]
                        combined_success = len(combined_data[combined_data["STATUS"].str.contains("SUCCESS", case=False, na=False)]) / len(combined_data)

                        recommended_combinations.append({
                            "products": [product1, product2],
                            "shared_owners": overlap,
                            "combined_success_rate": round(combined_success, 3),
                            "confidence": min(1.0, overlap / 5.0)  # Confidence based on number of shared owners
                        })

            # Find outlier owners (those with very different product usage patterns)
            cluster_owners = cluster_data["APPOWNER"].unique()
            outlier_owners = []

            for owner in cluster_owners:
                owner_products = set(cluster_data[cluster_data["APPOWNER"] == owner]["PRODUCT"])
                dominant_products_set = set(dominant_products)

                # If owner uses less than 30% of dominant products, they might be an outlier
                overlap_ratio = len(owner_products & dominant_products_set) / len(dominant_products_set)
                if overlap_ratio < 0.3 and len(owner_products) >= 2:
                    outlier_owners.append(owner)

            insights.append(ProductClusterInsight(
                cluster_id=f"Cluster {cluster}",
                dominant_products=dominant_products,
                success_pattern=success_pattern,
                recommended_product_combinations=recommended_combinations,
                outlier_owners=outlier_owners[:5]  # Limit to top 5
            ))

        return insights

    def _generate_intelligent_recommendations(self, df: pd.DataFrame, owner_insights: List[OwnerBehaviorInsight], cluster_insights: List[ProductClusterInsight]) -> List[IntelligentRecommendation]:
        """Generate intelligent recommendations based on behavior and cluster analysis."""
        recommendations = []

        # Owner behavior recommendations
        high_risk_owners = [insight for insight in owner_insights if insight.risk_level == "high"]
        if len(high_risk_owners) > 0:
            recommendations.append(IntelligentRecommendation(
                type="owner_behavior",
                priority="critical",
                title=f"Critical: {len(high_risk_owners)} owners with inconsistent remediation patterns",
                description=f"Owners like {', '.join([o.owner for o in high_risk_owners[:3]])} show significant performance gaps between products.",
                affected_owners=[o.owner for o in high_risk_owners],
                affected_products=list(set([p for o in high_risk_owners for p in o.low_performing_products])),
                confidence_score=0.9,
                potential_impact="High - Could improve overall remediation success rate by 15-25%",
                suggested_actions=[
                    "Provide targeted training for low-performing products",
                    "Implement peer mentoring with high-performing owners",
                    "Review and standardize remediation processes"
                ]
            ))

        # Product cluster recommendations
        for cluster_insight in cluster_insights:
            if len(cluster_insight.recommended_product_combinations) > 0:
                best_combo = max(cluster_insight.recommended_product_combinations, key=lambda x: x["combined_success_rate"])

                recommendations.append(IntelligentRecommendation(
                    type="product_cluster",
                    priority="high",
                    title=f"Optimize {cluster_insight.cluster_id}: Promote product combinations",
                    description=f"Products {' + '.join(best_combo['products'])} show {best_combo['combined_success_rate']*100:.1f}% success rate when used together.",
                    affected_owners=[],
                    affected_products=best_combo["products"],
                    confidence_score=best_combo["confidence"],
                    potential_impact="Medium - Could improve cluster efficiency by 10-15%",
                    suggested_actions=[
                        f"Encourage owners to adopt both {' and '.join(best_combo['products'])}",
                        "Create combined remediation workflows",
                        "Share best practices from successful combinations"
                    ]
                ))

        # Cross-remediation opportunities
        neglected_products_count = {}
        for insight in owner_insights:
            for product in insight.neglected_products:
                neglected_products_count[product] = neglected_products_count.get(product, 0) + 1

        if neglected_products_count:
            most_neglected = max(neglected_products_count.items(), key=lambda x: x[1])
            if most_neglected[1] >= 3:  # At least 3 owners are neglecting this product
                recommendations.append(IntelligentRecommendation(
                    type="cross_remediation",
                    priority="medium",
                    title=f"Adoption Opportunity: {most_neglected[0]} is underutilized",
                    description=f"{most_neglected[1]} owners in similar clusters could benefit from adopting {most_neglected[0]}.",
                    affected_owners=[o.owner for o in owner_insights if most_neglected[0] in o.neglected_products],
                    affected_products=[most_neglected[0]],
                    confidence_score=0.7,
                    potential_impact="Medium - Could expand remediation coverage",
                    suggested_actions=[
                        f"Evaluate {most_neglected[0]} for broader adoption",
                        "Provide training and documentation",
                        "Start with pilot implementations"
                    ]
                ))

        # Sort by priority
        priority_order = {"critical": 4, "high": 3, "medium": 2, "low": 1}
        recommendations.sort(key=lambda x: priority_order[x.priority], reverse=True)

        return recommendations

    def _calculate_priority_score(self, row: pd.Series) -> float:
        """Calculates priority score based on various factors."""
        score = 0.0 # Start with float
        try:
            # Cluster weight - handle potential non-numeric cluster names if necessary
            cluster_key = f"cluster_{row.get('CLUSTER', 'unknown')}"
            score += self.priority_weights.get(cluster_key, 1.0)

            # Environment weight
            env_weight = self.priority_weights["Production"] if row.get("ENVIRONMENT") == "Production" else self.priority_weights["Non-Production"]
            score += env_weight

            # Path usage weight (example logic, adjust as needed)
            path_usage_str = str(row.get("PATH_USAGE", "")).lower()
            path_weight = self.priority_weights["path_frequent"] if "frequently" in path_usage_str else 1.0
            score += path_weight

            # Success history weight
            if row.get("STATUS") == "SUCCESS":
                score += self.priority_weights["success_history"]

        except Exception as e:
            logger.warning(f"Error calculating priority score for row {row.get('NAME', 'N/A')}: {e}. Returning 0.")
            return 0.0
        return round(score, 2) # Return rounded score

    def _safe_get_mode(self, series: pd.Series) -> Optional[str]:
        """Safely get the mode, returning None if empty or error."""
        if series.empty or series.mode().empty:
            return None
        try:
            # Ensure result is string, handle potential non-string modes if necessary
            return str(series.mode().iloc[0])
        except Exception as e:
            logger.warning(f"Error getting mode for series: {e}")
            return None

    def _create_server_data(self, row: pd.Series) -> ServerData:
        """Creates ServerData object from a DataFrame row."""
        l_levels = {}
        for col in self.l_level_columns:
            # Check if column exists and value is not null/NA
            if col in row and pd.notna(row[col]):
                l_levels[col] = str(row[col])

        # Also include the APP_LEVELS column if it exists and has data
        if self.app_levels_column in row and pd.notna(row[self.app_levels_column]):
             # Ensure this column actually contains L-Levels, not owner names etc.
             # Add validation here if necessary based on expected format
            l_levels[self.app_levels_column] = str(row[self.app_levels_column])

        # Handle timestamp conversion safely
        last_update_str = None
        if "TIMESTAMP" in row and pd.notna(row["TIMESTAMP"]):
            try:
                # Attempt parsing known format, fallback to ISO format or now()
                # Adjust format string "%Y-%m-%d %H:%M:%S" if needed
                ts = pd.to_datetime(row["TIMESTAMP"], format="%Y-%m-%d %H:%M:%S", errors='coerce')
                if pd.notna(ts):
                    last_update_str = ts.isoformat()
                else:
                    # Try parsing as ISO format directly if previous failed
                    ts = pd.to_datetime(row["TIMESTAMP"], errors='coerce')
                    if pd.notna(ts):
                        last_update_str = ts.isoformat()

            except Exception as e:
                logger.warning(f"Could not parse timestamp '{row['TIMESTAMP']}': {e}")
        # If still None, maybe default to None or a placeholder? For now, None.

        return ServerData(
            id=str(row.get("NAME", "N/A")), # Use .get for safety
            environment=str(row.get("ENVIRONMENT", "Unknown")),
            status=str(row.get("STATUS", "Unknown")),
            last_update=last_update_str,
            cluster=str(row.get("CLUSTER", "Unknown")),
            products=[str(p) for p in [row.get("PRODUCT")] if pd.notna(p)], # Handle single product
            operations=[str(o) for o in [row.get("OPERATION")] if pd.notna(o)], # Handle single operation
            l_levels=l_levels if l_levels else None
        )

    def _determine_app_l_level(self, servers: List[ServerData]) -> Optional[str]:
        """
        Determine a representative L-level for an app based on its servers.
        Prioritizes L4/L5, then falls back to APP_LEVELS.
        Returns 'L4', 'L5', or the first value from APP_LEVELS, or None.
        NEVER returns owner name.
        """
        l_level_counts = {}
        # Prioritize specific L-levels (e.g., L4 or L5) if available across servers
        target_levels = ["CMDB_L4_NAME", "CMDB_L5_NAME"] # Prioritize these

        for server in servers:
            if server.l_levels:
                for target_col in target_levels:
                    if target_col in server.l_levels and server.l_levels[target_col]:
                         # Use just the level name (L4/L5) for counting dominance
                         level_name_only = target_col.split('_')[1] # Extracts L4 or L5
                         l_level_counts[level_name_only] = l_level_counts.get(level_name_only, 0) + 1

        if l_level_counts:
            # Return the most frequent L4/L5 level name found
            most_frequent_level = max(l_level_counts, key=l_level_counts.get)
            logger.debug(f"Determined L-Level '{most_frequent_level}' from L4/L5 counts.")
            return most_frequent_level # Returns "L4" or "L5"

        # Fallback: Check APP_LEVELS if L4/L5 not dominant
        app_level_values_found = []
        for server in servers:
             if server.l_levels and self.app_levels_column in server.l_levels:
                 app_level_str = server.l_levels[self.app_levels_column]
                 # Check if the value looks like an L-Level (e.g., starts with 'L')
                 # Add more robust checking if needed
                 if app_level_str and isinstance(app_level_str, str):
                      # Split in case of multiple values, take the first valid-looking one
                      possible_levels = [lvl.strip() for lvl in app_level_str.split(',') if lvl.strip().startswith('L')]
                      if possible_levels:
                           app_level_values_found.append(possible_levels[0])
                           # Optionally break if first valid one is found, or collect all and find mode
                           break # Taking the first valid one found

        if app_level_values_found:
             # Simple approach: return the first valid L-Level found in APP_LEVELS
             logger.debug(f"Determined L-Level '{app_level_values_found[0]}' from APP_LEVELS column.")
             return app_level_values_found[0]

        logger.debug("Could not determine a representative L-Level for the app.")
        return None # No representative level found

    def find_owner_primary_l_level(self, owner_df: pd.DataFrame) -> Tuple[str, str]:
        """
        Find the primary L-level column and value for a given owner's data.

        Args:
            owner_df: DataFrame containing data for a specific owner

        Returns:
            Tuple of (l_level_column, l_level_value)
        """
        # Priority order for L-level columns (most important first)
        l_level_priority = ["CMDB_L4_NAME", "CMDB_L5_NAME", "CMDB_L3_NAME", "CMDB_L6_NAME", "CMDB_L7_NAME"]

        for col in l_level_priority:
            if col in owner_df.columns:
                # Get the most common non-null value for this L-level
                values = owner_df[col].dropna()
                if not values.empty:
                    most_common_value = values.mode().iloc[0]
                    logger.info(f"Found primary L-level for owner: {col}={most_common_value}")
                    return col, most_common_value

        # Fallback if no L-level columns have values
        logger.warning("Could not determine primary L-level for owner")
        return "CMDB_L4_NAME", ""  # Default fallback

    def calculate_similarity(self, peer_owners_df: pd.DataFrame, target_owner_data: pd.DataFrame) -> pd.Series:
        """
        Calculate similarity scores between target owner and peer owners.

        Args:
            peer_owners_df: DataFrame containing data for peer owners
            target_owner_data: DataFrame containing data for target owner

        Returns:
            Series with similarity scores for each peer owner
        """
        # Get unique owners in peer_owners_df
        peer_owners = peer_owners_df["APPOWNER"].unique()
        similarity_scores = {}

        # Target owner characteristics
        target_products = set(target_owner_data["PRODUCT"].dropna())
        target_operations = set(target_owner_data["OPERATION"].dropna())
        target_environments = target_owner_data["ENVIRONMENT"].value_counts().to_dict()
        target_clusters = target_owner_data["CLUSTER"].value_counts().to_dict()
        target_priority_avg = target_owner_data["priority_score"].mean() if "priority_score" in target_owner_data.columns else 0

        # Calculate similarity for each peer owner
        for owner in peer_owners:
            # Skip if this is the target owner
            if owner in target_owner_data["APPOWNER"].values:
                continue

            owner_data = peer_owners_df[peer_owners_df["APPOWNER"] == owner]

            # Calculate various similarity metrics
            owner_products = set(owner_data["PRODUCT"].dropna())
            owner_operations = set(owner_data["OPERATION"].dropna())

            # Product overlap (Jaccard similarity)
            product_similarity = len(target_products.intersection(owner_products)) / max(1, len(target_products.union(owner_products)))

            # Operation overlap
            operation_similarity = len(target_operations.intersection(owner_operations)) / max(1, len(target_operations.union(owner_operations)))

            # Priority score similarity (inverse of absolute difference)
            owner_priority_avg = owner_data["priority_score"].mean() if "priority_score" in owner_data.columns else 0
            priority_diff = abs(target_priority_avg - owner_priority_avg)
            priority_similarity = 1 / (1 + priority_diff)  # Normalize to 0-1 range

            # Combine metrics with weights
            similarity = (
                0.4 * product_similarity +  # Products are most important
                0.3 * operation_similarity +  # Operations are next
                0.3 * priority_similarity  # Priority score is also important
            )

            similarity_scores[owner] = similarity

        # Convert to Series
        return pd.Series(similarity_scores)

    def _calculate_remediation_stats(self, df: pd.DataFrame) -> RemediationStats:
        """Calculate comprehensive remediation statistics."""
        total_items = len(df)

        # Count different status types
        success_count = len(df[df["STATUS"] == "SUCCESS"])
        failed_count = len(df[df["STATUS"] == "FAILED"])
        unknown_count = len(df[df["STATUS"] == "UNKNOWN"])

        # Items that were executed (remediated) vs not executed (pending)
        executed_df = df[df["EXECUTION"] == "yes"]
        remediated_count = len(executed_df)
        pending_count = total_items - remediated_count

        # Calculate success rate
        success_rate = (success_count / total_items * 100) if total_items > 0 else 0

        return RemediationStats(
            total_items=total_items,
            remediated_count=remediated_count,
            pending_count=pending_count,
            success_rate=round(success_rate, 2),
            failed_count=failed_count,
            unknown_count=unknown_count
        )

    def _get_top_5_applications(self, df: pd.DataFrame) -> List[Dict[str, any]]:
        """Get top 5 applications based on remediation activity and priority."""
        if df.empty:
            return []

        # Group by application and calculate metrics
        app_stats = []
        for app_name, app_group in df.groupby("APP_NAME"):
            if pd.notna(app_name):
                total_items = len(app_group)
                remediated_items = len(app_group[app_group["EXECUTION"] == "yes"])
                success_items = len(app_group[app_group["STATUS"] == "SUCCESS"])
                avg_priority = app_group["priority_score"].mean()
                unique_servers = app_group["NAME"].nunique()
                unique_products = app_group["PRODUCT"].nunique()

                # Calculate remediation rate
                remediation_rate = (remediated_items / total_items * 100) if total_items > 0 else 0
                success_rate = (success_items / total_items * 100) if total_items > 0 else 0

                app_stats.append({
                    "name": str(app_name),
                    "total_items": total_items,
                    "remediated_items": remediated_items,
                    "pending_items": total_items - remediated_items,
                    "success_items": success_items,
                    "remediation_rate": round(remediation_rate, 2),
                    "success_rate": round(success_rate, 2),
                    "avg_priority_score": round(avg_priority, 2),
                    "unique_servers": unique_servers,
                    "unique_products": unique_products,
                    "owner": app_group["APPOWNER"].iloc[0] if not app_group["APPOWNER"].empty else "Unknown"
                })

        # Sort by remediation activity (combination of total items and remediation rate)
        app_stats.sort(key=lambda x: (x["total_items"] * x["remediation_rate"] / 100), reverse=True)

        return app_stats[:5]

    def _analyze_product_correlations(self, df: pd.DataFrame) -> Dict[str, any]:
        """Analyze correlations between products, operations, and success rates."""
        if df.empty:
            return {"correlations": [], "insights": []}

        correlations = []
        insights = []

        # Product-Operation correlations
        for product in df["PRODUCT"].dropna().unique():
            product_df = df[df["PRODUCT"] == product]

            # Get operation distribution for this product
            operations = product_df["OPERATION"].value_counts().head(3)
            success_rate = len(product_df[product_df["STATUS"] == "SUCCESS"]) / len(product_df) * 100

            correlations.append({
                "product": str(product),
                "top_operations": operations.to_dict(),
                "success_rate": round(success_rate, 2),
                "total_executions": len(product_df),
                "environments": product_df["ENVIRONMENT"].value_counts().to_dict()
            })

        # Sort by success rate
        correlations.sort(key=lambda x: x["success_rate"], reverse=True)

        # Generate insights
        if correlations:
            best_product = correlations[0]
            worst_product = correlations[-1]

            insights.append(f"Best performing product: {best_product['product']} with {best_product['success_rate']}% success rate")
            insights.append(f"Lowest performing product: {worst_product['product']} with {worst_product['success_rate']}% success rate")

            # Find products with high execution count but low success rate
            high_volume_low_success = [p for p in correlations if p["total_executions"] > 10 and p["success_rate"] < 50]
            if high_volume_low_success:
                insights.append(f"High-volume, low-success products need attention: {', '.join([p['product'] for p in high_volume_low_success[:3]])}")

        return {
            "correlations": correlations[:10],  # Top 10 products
            "insights": insights
        }

    def _calculate_product_correlation_matrix(self, df: pd.DataFrame) -> Dict[str, Dict[str, float]]:
        """Calculate correlation matrix between products based on co-occurrence and success patterns."""
        if df.empty:
            return {}

        products = df["PRODUCT"].dropna().unique()
        correlation_matrix = {}

        for product1 in products:
            correlation_matrix[str(product1)] = {}
            product1_data = df[df["PRODUCT"] == product1]

            for product2 in products:
                if product1 == product2:
                    correlation_matrix[str(product1)][str(product2)] = 1.0
                    continue

                product2_data = df[df["PRODUCT"] == product2]

                # Find common owners/applications
                common_owners = set(product1_data["APPOWNER"].dropna()) & set(product2_data["APPOWNER"].dropna())
                common_apps = set(product1_data["APP_NAME"].dropna()) & set(product2_data["APP_NAME"].dropna())

                # Calculate correlation based on multiple factors
                co_occurrence_score = len(common_owners) / max(1, len(set(product1_data["APPOWNER"].dropna()).union(set(product2_data["APPOWNER"].dropna()))))

                # Success rate correlation
                product1_success = len(product1_data[product1_data["STATUS"] == "SUCCESS"]) / max(1, len(product1_data))
                product2_success = len(product2_data[product2_data["STATUS"] == "SUCCESS"]) / max(1, len(product2_data))
                success_correlation = 1 - abs(product1_success - product2_success)

                # Environment/cluster correlation
                env_overlap = len(set(product1_data["ENVIRONMENT"].dropna()) & set(product2_data["ENVIRONMENT"].dropna()))
                cluster_overlap = len(set(product1_data["CLUSTER"].dropna()) & set(product2_data["CLUSTER"].dropna()))

                # Combined correlation score
                correlation = (
                    0.4 * co_occurrence_score +
                    0.3 * success_correlation +
                    0.2 * (env_overlap / max(1, len(set(product1_data["ENVIRONMENT"].dropna()).union(set(product2_data["ENVIRONMENT"].dropna()))))) +
                    0.1 * (cluster_overlap / max(1, len(set(product1_data["CLUSTER"].dropna()).union(set(product2_data["CLUSTER"].dropna())))))
                )

                correlation_matrix[str(product1)][str(product2)] = round(correlation, 3)

        return correlation_matrix

    def _generate_product_to_product_recommendations(self, df: pd.DataFrame) -> List[ProductRecommendation]:
        """Generate product-to-product recommendations based on correlation analysis."""
        if df.empty:
            return []

        recommendations = []
        correlation_matrix = self._calculate_product_correlation_matrix(df)

        for target_product, correlations in correlation_matrix.items():
            # Sort by correlation score, excluding self-correlation
            sorted_correlations = sorted(
                [(prod, score) for prod, score in correlations.items() if prod != target_product and score > 0.3],
                key=lambda x: x[1], reverse=True
            )

            # Take top 3 recommendations per product
            for recommended_product, correlation_score in sorted_correlations[:3]:
                target_data = df[df["PRODUCT"] == target_product]
                recommended_data = df[df["PRODUCT"] == recommended_product]

                # Calculate additional metrics
                target_success_rate = len(target_data[target_data["STATUS"] == "SUCCESS"]) / max(1, len(target_data))
                recommended_success_rate = len(recommended_data[recommended_data["STATUS"] == "SUCCESS"]) / max(1, len(recommended_data))

                # Co-occurrence count
                common_owners = set(target_data["APPOWNER"].dropna()) & set(recommended_data["APPOWNER"].dropna())
                co_occurrence_count = len(common_owners)

                # Statistical significance (simple measure based on sample size)
                min_sample_size = min(len(target_data), len(recommended_data))
                statistical_significance = min(1.0, min_sample_size / 30.0)  # Normalize to 0-1

                # Success rate improvement
                success_rate_improvement = recommended_success_rate - target_success_rate

                # Confidence level based on correlation and sample size
                confidence_level = correlation_score * statistical_significance

                # Generate reasoning
                reasoning = f"Products frequently managed together by {co_occurrence_count} common owners. "
                if success_rate_improvement > 0:
                    reasoning += f"Switching could improve success rate by {success_rate_improvement*100:.1f}%."
                else:
                    reasoning += f"Both products show similar success patterns."

                recommendations.append(ProductRecommendation(
                    target_product=target_product,
                    recommended_product=recommended_product,
                    correlation_score=correlation_score,
                    confidence_level=confidence_level,
                    reasoning=reasoning,
                    statistical_significance=statistical_significance,
                    co_occurrence_count=co_occurrence_count,
                    success_rate_improvement=success_rate_improvement
                ))

        # Sort by confidence level and return top recommendations
        recommendations.sort(key=lambda x: x.confidence_level, reverse=True)
        return recommendations[:20]  # Top 20 recommendations

    def _analyze_owner_product_affinities(self, df: pd.DataFrame) -> List[OwnerProductAffinity]:
        """Analyze which products each owner has highest affinity/expertise with."""
        if df.empty:
            return []

        affinities = []

        for owner in df["APPOWNER"].dropna().unique():
            owner_data = df[df["APPOWNER"] == owner]

            # Analyze each product this owner works with
            for product in owner_data["PRODUCT"].dropna().unique():
                product_data = owner_data[owner_data["PRODUCT"] == product]

                execution_count = len(product_data)
                success_count = len(product_data[product_data["STATUS"] == "SUCCESS"])
                success_rate = success_count / max(1, execution_count)

                # Calculate affinity score based on frequency and success
                frequency_score = min(1.0, execution_count / 20.0)  # Normalize frequency
                affinity_score = (0.6 * success_rate + 0.4 * frequency_score)

                # Determine expertise level
                if success_rate >= 0.8 and execution_count >= 10:
                    expertise_level = "Expert"
                elif success_rate >= 0.6 and execution_count >= 5:
                    expertise_level = "Proficient"
                elif success_rate >= 0.4:
                    expertise_level = "Developing"
                else:
                    expertise_level = "Novice"

                # Generate product recommendations for this owner
                recommended_products = self._get_owner_product_recommendations(df, owner, product)

                affinities.append(OwnerProductAffinity(
                    owner=str(owner),
                    product=str(product),
                    affinity_score=round(affinity_score, 3),
                    success_rate=round(success_rate, 3),
                    execution_count=execution_count,
                    expertise_level=expertise_level,
                    recommended_products=recommended_products
                ))

        # Sort by affinity score
        affinities.sort(key=lambda x: x.affinity_score, reverse=True)
        return affinities

    def _get_owner_product_recommendations(self, df: pd.DataFrame, owner: str, current_product: str) -> List[ProductRecommendation]:
        """Get product recommendations for a specific owner based on their expertise."""
        owner_data = df[df["APPOWNER"] == owner]
        current_product_data = owner_data[owner_data["PRODUCT"] == current_product]

        if current_product_data.empty:
            return []

        recommendations = []
        correlation_matrix = self._calculate_product_correlation_matrix(df)

        if current_product not in correlation_matrix:
            return []

        # Get correlated products
        correlations = correlation_matrix[current_product]
        sorted_correlations = sorted(
            [(prod, score) for prod, score in correlations.items() if prod != current_product and score > 0.2],
            key=lambda x: x[1], reverse=True
        )

        for recommended_product, correlation_score in sorted_correlations[:3]:
            # Check if owner has experience with recommended product
            owner_recommended_data = owner_data[owner_data["PRODUCT"] == recommended_product]

            if not owner_recommended_data.empty:
                # Owner has experience - calculate improvement potential
                current_success = len(current_product_data[current_product_data["STATUS"] == "SUCCESS"]) / max(1, len(current_product_data))
                recommended_success = len(owner_recommended_data[owner_recommended_data["STATUS"] == "SUCCESS"]) / max(1, len(owner_recommended_data))
                success_improvement = recommended_success - current_success
                reasoning = f"You have {len(owner_recommended_data)} executions with {recommended_success*100:.1f}% success rate on this product."
            else:
                # Owner has no experience - base on overall product performance
                all_recommended_data = df[df["PRODUCT"] == recommended_product]
                overall_success = len(all_recommended_data[all_recommended_data["STATUS"] == "SUCCESS"]) / max(1, len(all_recommended_data))
                success_improvement = overall_success - (len(current_product_data[current_product_data["STATUS"] == "SUCCESS"]) / max(1, len(current_product_data)))
                reasoning = f"Similar product with {overall_success*100:.1f}% overall success rate. Consider expanding expertise."

            recommendations.append(ProductRecommendation(
                target_product=current_product,
                recommended_product=recommended_product,
                correlation_score=correlation_score,
                confidence_level=correlation_score * 0.8,  # Slightly lower confidence for individual recommendations
                reasoning=reasoning,
                statistical_significance=min(1.0, len(current_product_data) / 10.0),
                co_occurrence_count=len(set(df[df["PRODUCT"] == current_product]["APPOWNER"].dropna()) &
                                       set(df[df["PRODUCT"] == recommended_product]["APPOWNER"].dropna())),
                success_rate_improvement=success_improvement
            ))

        return recommendations

    def _generate_cross_product_recommendations(self, df: pd.DataFrame) -> List[CrossProductRecommendation]:
        """Generate cross-product recommendations based on environment and cluster patterns."""
        if df.empty:
            return []

        recommendations = []
        correlation_matrix = self._calculate_product_correlation_matrix(df)

        # Analyze environment and cluster patterns
        for primary_product in df["PRODUCT"].dropna().unique():
            primary_data = df[df["PRODUCT"] == primary_product]

            # Get environment and cluster distributions for primary product
            primary_envs = primary_data["ENVIRONMENT"].value_counts().to_dict()
            primary_clusters = primary_data["CLUSTER"].value_counts().to_dict()

            # Find correlated products
            if str(primary_product) in correlation_matrix:
                correlations = correlation_matrix[str(primary_product)]
                sorted_correlations = sorted(
                    [(prod, score) for prod, score in correlations.items() if prod != str(primary_product) and score > 0.4],
                    key=lambda x: x[1], reverse=True
                )

                for secondary_product, correlation_coefficient in sorted_correlations[:2]:  # Top 2 per primary
                    secondary_data = df[df["PRODUCT"] == secondary_product]

                    # Analyze environment overlap
                    secondary_envs = secondary_data["ENVIRONMENT"].value_counts().to_dict()
                    common_envs = set(primary_envs.keys()) & set(secondary_envs.keys())
                    env_pattern = f"Both products used in: {', '.join(list(common_envs)[:3])}" if common_envs else "Different environments"

                    # Analyze cluster overlap
                    secondary_clusters = secondary_data["CLUSTER"].value_counts().to_dict()
                    common_clusters = set(primary_clusters.keys()) & set(secondary_clusters.keys())
                    cluster_pattern = f"Common clusters: {', '.join(list(common_clusters)[:3])}" if common_clusters else "Different clusters"

                    # Calculate combined success rate
                    primary_success = len(primary_data[primary_data["STATUS"] == "SUCCESS"]) / max(1, len(primary_data))
                    secondary_success = len(secondary_data[secondary_data["STATUS"] == "SUCCESS"]) / max(1, len(secondary_data))
                    combined_success_rate = (primary_success + secondary_success) / 2

                    # Calculate confidence score based on correlation and overlap
                    env_overlap_score = len(common_envs) / max(1, len(set(primary_envs.keys()).union(set(secondary_envs.keys()))))
                    cluster_overlap_score = len(common_clusters) / max(1, len(set(primary_clusters.keys()).union(set(secondary_clusters.keys()))))
                    confidence_score = correlation_coefficient * (0.5 * env_overlap_score + 0.5 * cluster_overlap_score)

                    # Generate reasoning
                    reasoning = f"Products show {correlation_coefficient:.1%} correlation. "
                    if env_overlap_score > 0.5:
                        reasoning += f"Frequently used together in {', '.join(list(common_envs)[:2])} environments. "
                    if combined_success_rate > 0.7:
                        reasoning += f"Combined approach shows {combined_success_rate:.1%} success rate."
                    else:
                        reasoning += f"Consider sequential implementation for better results."

                    recommendations.append(CrossProductRecommendation(
                        primary_product=str(primary_product),
                        secondary_product=str(secondary_product),
                        correlation_coefficient=correlation_coefficient,
                        confidence_score=round(confidence_score, 3),
                        environment_pattern=env_pattern,
                        cluster_pattern=cluster_pattern,
                        success_rate_combined=round(combined_success_rate, 3),
                        reasoning=reasoning
                    ))

        # Sort by confidence score
        recommendations.sort(key=lambda x: x.confidence_score, reverse=True)
        return recommendations[:15]  # Top 15 cross-product recommendations

    def _generate_product_level_recommendations(self, df: pd.DataFrame) -> ProductLevelRecommendations:
        """Generate comprehensive product-level recommendations using caching."""
        logger.info("Generating product-level recommendations using optimized caching")

        if df.empty:
            return ProductLevelRecommendations(
                product_to_product=[],
                owner_product_affinities=[],
                cross_product_recommendations=[],
                correlation_matrix={},
                insights=[],
                owner_behavior_insights=[],
                product_cluster_insights=[],
                intelligent_recommendations=[]
            )

        # Use cached/optimized approach
        return self._get_cached_or_compute_recommendations(df)

    def _create_app_data(self, app_name: str, app_group: pd.DataFrame) -> AppData:
        """Creates AppData object from a grouped DataFrame."""
        servers = [self._create_server_data(row) for _, row in app_group.iterrows()]
        app_l_level = self._determine_app_l_level(servers) # Determine representative L-level

        # Ensure app_l_level is not accidentally set to the owner name here
        # This function should only return L-Levels like 'L4', 'L5' or None

        return AppData(
            name=app_name, # Use the passed app_name
            priority_score=round(app_group["priority_score"].mean(), 2), # Calculate mean score
            servers=servers,
            cluster_distribution=app_group["CLUSTER"].value_counts().to_dict(),
            status_summary=app_group["STATUS"].value_counts().to_dict(),
            most_common_product=self._safe_get_mode(app_group["PRODUCT"]),
            most_common_operation=self._safe_get_mode(app_group["OPERATION"]),
            l_level=app_l_level # Assign determined L-level ('L4', 'L5', or None)
        )

    def _create_owner_data(self, owner: str, owner_group: pd.DataFrame) -> OwnerData:
        """Creates OwnerData object from a grouped DataFrame."""
        apps_data = []
        # Group within the owner's data by APP_NAME
        for app_name, app_group in owner_group.groupby("APP_NAME"):
            if pd.notna(app_name): # Ensure app_name is valid
                 apps_data.append(self._create_app_data(str(app_name), app_group))

        # Sort apps within the owner by priority score
        apps_data.sort(key=lambda x: x.priority_score, reverse=True)

        return OwnerData(
            owner=str(owner).strip(), # Ensure owner is string and stripped
            total_apps=len(owner_group["APP_NAME"].dropna().unique()), # Count unique non-null apps
            total_servers=len(owner_group["NAME"].dropna().unique()), # Count unique non-null servers
            environment_distribution=owner_group["ENVIRONMENT"].value_counts().to_dict(),
            apps=apps_data
        )

    def _generate_organizational_recommendations(self, df: pd.DataFrame) -> dict:
        """Generate organizational recommendations based on L-levels (Placeholder/Example)."""
        # This function needs refinement based on actual desired logic for org recs
        org_recs = {}
        l_level_map = { "CMDB_L4_NAME": "L4", "CMDB_L5_NAME": "L5", "CMDB_L6_NAME": "L6L7", "CMDB_L7_NAME": "L6L7", "CMDB_L8_NAME": "L6L7" }
        for l_col, l_key in l_level_map.items():
            if l_col in df.columns:
                level_df = df[df[l_col].notna()]
                if not level_df.empty:
                    level_name_val = self._safe_get_mode(level_df[l_col])
                    level_name = f"{l_key} - {level_name_val}" if level_name_val else l_key
                    top_product = self._safe_get_mode(level_df["PRODUCT"])
                    top_op = self._safe_get_mode(level_df["OPERATION"])
                    product_stats, op_stats = {}, {}
                    if top_product: prod_df = level_df[level_df["PRODUCT"] == top_product]; execs = len(prod_df); success = len(prod_df[prod_df["STATUS"] == "SUCCESS"]); rate = (success / execs * 100) if execs > 0 else 0; product_stats = {"executions": execs, "success_rate": rate}
                    if top_op: op_df = level_df[level_df["OPERATION"] == top_op]; execs = len(op_df); success = len(op_df[op_df["STATUS"] == "SUCCESS"]); rate = (success / execs * 100) if execs > 0 else 0; op_stats = {"executions": execs, "success_rate": rate}
                    if l_key not in org_recs: org_recs[l_key] = { "level": level_name, "product": [top_product, product_stats] if top_product else None, "operation": [top_op, op_stats] if top_op else None, "description": f"Recommendations for {level_name}.", "apps_affected": list(level_df['APP_NAME'].dropna().unique())[:5] }
        return org_recs

    def _generate_product_recommendations(self, df: pd.DataFrame) -> dict:
        """Generate product rankings and cross-product recommendations (Placeholder/Example)."""
        if df.empty: return {"top_products": [], "cross_product_recommendations": []}
        products_stats = {}
        for product in df["PRODUCT"].dropna().unique(): product_df = df[df["PRODUCT"] == product]; executions = len(product_df); success_count = len(product_df[product_df["STATUS"] == "SUCCESS"]); success_rate = (success_count / executions) * 100 if executions > 0 else 0; products_stats[product] = {"executions": executions, "success_count": success_count, "success_rate": success_rate}
        sorted_products = sorted(products_stats.items(), key=lambda x: x[1]["executions"], reverse=True); top_products = sorted_products[:5]
        cross_product_recs = []
        if len(top_products) >= 2: p1, s1 = top_products[0]; p2, s2 = top_products[1]; cross_product_recs.append({ "products": [p1, p2], "combined_success": round((s1.get("success_rate",0) + s2.get("success_rate",0)) / 2, 0), "description": f"Consider using {p1} and {p2} together." })
        return { "top_products": top_products, "cross_product_recommendations": cross_product_recs }

    def _generate_product_clusters(self, df: pd.DataFrame) -> dict:
        """Generate node-link data for cluster visualization."""
        logger.info(f"Generating product clusters from DataFrame with {len(df)} rows.")
        if df.empty: logger.warning("Cannot generate clusters: DataFrame is empty."); return {"nodes": [], "links": []}
        nodes, links, node_ids, next_id = [], [], {}, 0
        # Add Owner Nodes
        for owner in df["APPOWNER"].dropna().unique():
            if f"owner_{owner}" not in node_ids: node_ids[f"owner_{owner}"] = next_id; nodes.append({"id": next_id, "name": str(owner), "type": "owner"}); next_id += 1
        # Add Product Nodes
        for product in df["PRODUCT"].dropna().unique():
             if f"product_{product}" not in node_ids:
                node_ids[f"product_{product}"] = next_id; product_df = df[df["PRODUCT"] == product]; success_rate = 0
                if not product_df.empty: success_count = len(product_df[product_df["STATUS"] == "SUCCESS"]); success_rate = (success_count / len(product_df)) * 100
                status = "patched" if success_rate >= 75 else "unpatched"; nodes.append({"id": next_id, "name": str(product), "type": "product", "status": status}); next_id += 1
        # Add L-Level Nodes (Example - Needs refinement based on desired hierarchy)
        l_level_values = set()
        for col in ["CMDB_L4_NAME", "CMDB_L5_NAME"]: # Focus on L4/L5 for distinct nodes?
             if col in df.columns: l_level_values.update(df[col].dropna().astype(str).unique())
        for level_val in l_level_values:
             level_type_prefix = "L4_" if "L4" in level_val else ("L5_" if "L5" in level_val else "L?") # Basic type detection
             node_key = f"llevel_{level_val}"
             if node_key not in node_ids: node_ids[node_key] = next_id; nodes.append({"id": next_id, "name": level_val, "type": "llevel"}); next_id += 1

        # Add Links (Owner-Product, Owner-L-Level - Needs refinement)
        owner_product_links = set(); owner_llevel_links = set()
        for _, row in df.iterrows():
            owner, product = row.get("APPOWNER"), row.get("PRODUCT")
            # Owner-Product Link
            if pd.notna(owner) and pd.notna(product):
                owner_key, product_key = f"owner_{owner}", f"product_{product}"
                if owner_key in node_ids and product_key in node_ids:
                    link_tuple = tuple(sorted((node_ids[owner_key], node_ids[product_key])));
                    if link_tuple not in owner_product_links: links.append({"source": node_ids[owner_key], "target": node_ids[product_key], "type": "uses", "value": 1 }); owner_product_links.add(link_tuple)
            # Owner-L-Level Link (Example: Link owner to their L4/L5 value if present)
            if pd.notna(owner):
                 owner_key = f"owner_{owner}"
                 for col in ["CMDB_L4_NAME", "CMDB_L5_NAME"]:
                     if col in row and pd.notna(row[col]):
                         level_val = str(row[col])
                         llevel_key = f"llevel_{level_val}"
                         if owner_key in node_ids and llevel_key in node_ids:
                             link_tuple = tuple(sorted((node_ids[owner_key], node_ids[llevel_key])))
                             if link_tuple not in owner_llevel_links: links.append({"source": node_ids[owner_key], "target": node_ids[llevel_key], "type": "belongs_to", "value": 1}); owner_llevel_links.add(link_tuple)
                             break # Link owner to first L4/L5 found for simplicity

        logger.info(f"Generated {len(nodes)} nodes and {len(links)} links for visualization.")
        return {"nodes": nodes, "links": links}


    def generate_recommendations(
        self,
        data_file: str = "clustered_data.csv",
        app_owner_filter: Optional[str] = None,
        frequency_filter: Optional[str] = None,
        app_name_filter: Optional[str] = None,
        product_filter: Optional[str] = None,
        l_level_filters: Optional[Dict[str, str]] = None,
        sort_by: str = "priority",
        sort_order: str = "desc"
    ) -> RecommendationReport:
        """
        Generates the full recommendation report, applying filters to the data.
        """
        try:
            logger.info(f"Loading data from CSV: {data_file}")
            if not os.path.exists(data_file): raise FileNotFoundError(f"Data file not found: {data_file}")
            df = pd.read_csv(data_file)
            logger.info(f"Initial data loaded with {len(df)} rows.")

            # --- Apply Priority Score ---
            df["priority_score"] = df.apply(self._calculate_priority_score, axis=1)

            # --- Apply Filters to DataFrame ---
            filtered_df = df.copy()
            if app_owner_filter:
                logger.info(f"Applying App Owner filter: {app_owner_filter}")
                filtered_df = filtered_df[filtered_df["APPOWNER"].astype(str).str.contains(app_owner_filter, case=False, na=False)]
                logger.info(f"Rows after App Owner filter: {len(filtered_df)}")
            if frequency_filter:
                logger.info(f"Applying Frequency filter: {frequency_filter}")
                try:
                    min_s, max_s = map(float, frequency_filter.split(","))
                    filtered_df = filtered_df[(filtered_df['priority_score'] >= min_s) & (filtered_df['priority_score'] <= max_s)]
                    logger.info(f"Rows after Frequency filter: {len(filtered_df)}")
                except ValueError:
                    logger.warning(f"Invalid frequency format: {frequency_filter}. Skipping.")
                except Exception as e:
                    logger.error(f"Error applying frequency filter '{frequency_filter}': {e}")
            if app_name_filter:
                logger.info(f"Applying App Name filter: {app_name_filter}")
                app_names = [name.strip() for name in app_name_filter.split(',')]
                filtered_df = filtered_df[filtered_df["APP_NAME"].astype(str).isin(app_names)]
                logger.info(f"Rows after App Name filter: {len(filtered_df)}")
            if product_filter:
                logger.info(f"Applying Product filter: {product_filter}")
                products = [product.strip() for product in product_filter.split(',')]
                filtered_df = filtered_df[filtered_df["PRODUCT"].astype(str).isin(products)]
                logger.info(f"Rows after Product filter: {len(filtered_df)}")
            if l_level_filters:
                logger.info(f"Applying L-Level filters: {l_level_filters}")
                for l_col, l_val in l_level_filters.items():
                    if l_col in filtered_df.columns:
                        filtered_df = filtered_df[filtered_df[l_col].astype(str).str.contains(str(l_val), case=False, na=False)]
                        logger.info(f"Rows after L-Level filter ({l_col}={l_val}): {len(filtered_df)}")
                    else:
                        logger.warning(f"L-Level column '{l_col}' not found. Skipping.")

            if filtered_df.empty:
                logger.warning("DataFrame empty after filtering. Returning empty report.")
                empty_remediation_stats = RemediationStats(0, 0, 0, 0.0, 0, 0)
                empty_summary = SummaryData(
                    total_owners=0, total_apps=0, total_servers=0,
                    environment_distribution={}, cluster_distribution={},
                    most_common_products={}, operation_types={},
                    remediation_stats=empty_remediation_stats, top_5_applications=[]
                )
                empty_metadata = {
                    "total_records": 0, "generated_at": pd.Timestamp.now().isoformat(),
                    "org_recommendations": {}, "product_recommendations": {"top_products": [], "cross_product_recommendations": []},
                    "product_correlations": {"correlations": [], "insights": []}
                }
                return RecommendationReport(summary=empty_summary, recommendations={}, metadata=empty_metadata)

            # --- Generate Report Components from Filtered Data ---
            recommendations_dict = {}
            owner_groups = filtered_df.groupby("APPOWNER"); logger.info(f"Processing {len(owner_groups)} owners after filtering.")
            for owner, group in owner_groups:
                if pd.notna(owner):
                    owner_key = str(owner)
                    recommendations_dict[owner_key] = self._create_owner_data(owner_key, group)
            # Generate enhanced analysis
            org_recommendations = self._generate_organizational_recommendations(filtered_df)
            product_recommendations = self._generate_product_recommendations(filtered_df)
            remediation_stats = self._calculate_remediation_stats(filtered_df)
            top_5_applications = self._get_top_5_applications(filtered_df)
            product_correlations = self._analyze_product_correlations(filtered_df)
            product_level_recommendations = self._generate_product_level_recommendations(filtered_df)

            total_owners, total_apps, total_servers = len(recommendations_dict), filtered_df["APP_NAME"].nunique(), filtered_df["NAME"].nunique()

            summary = SummaryData(
                total_owners=total_owners,
                total_apps=total_apps,
                total_servers=total_servers,
                environment_distribution=filtered_df["ENVIRONMENT"].value_counts().to_dict(),
                cluster_distribution=filtered_df["CLUSTER"].value_counts().to_dict(),
                most_common_products=filtered_df["PRODUCT"].value_counts().head(5).to_dict(),
                operation_types=filtered_df["OPERATION"].value_counts().to_dict(),
                remediation_stats=remediation_stats,
                top_5_applications=top_5_applications
            )

            metadata = {
                "total_records_after_filter": len(filtered_df),
                "total_records_before_filter": len(df),
                "applied_filters": {
                    "app_owner": app_owner_filter,
                    "frequency": frequency_filter,
                    "app_name": app_name_filter,
                    "product": product_filter,
                    "l_levels": l_level_filters,
                    "sort_by": sort_by,
                    "sort_order": sort_order
                },
                "generated_at": pd.Timestamp.now().isoformat(),
                "org_recommendations": org_recommendations,
                "product_recommendations": product_recommendations,
                "product_correlations": product_correlations,
                "product_level_recommendations": asdict(product_level_recommendations)
            }
            if sort_by == "owner_name": recommendations_dict = dict(sorted(recommendations_dict.items(), reverse=(sort_order.lower() == "desc")))
            report = RecommendationReport( summary=summary, recommendations=recommendations_dict, metadata=metadata )
            logger.info(f"Generated report with {total_owners} owners, {total_apps} apps, {total_servers} servers after filtering.")
            return report
        except FileNotFoundError as e: logger.error(f"Data file error: {e}"); raise
        except Exception as e: logger.exception(f"Unexpected error generating recommendations: {e}"); raise RuntimeError(f"Error generating recommendations: {e}")


# --- Flask App Creation ---
def create_app(config_class=Config):
    base_dir = os.path.dirname(os.path.abspath(__file__))
    app = Flask(__name__, template_folder=os.path.join(base_dir, "templates"), static_folder=os.path.join(base_dir, "assets"), static_url_path="/assets")
    app.config.from_object(config_class)
    recommendation_service = RecommendationService()
    bp = Blueprint("main", __name__)

    # --- Routes ---
    @bp.route("/")
    def index():
        try:
            data_file = "clustered_data_L.csv";
            if not os.path.exists(data_file): data_file = "clustered_data.csv"; logger.warning(f"L-level data file not found, falling back to {data_file}")
            recommendations = recommendation_service.generate_recommendations(data_file)
            org_funnel_data = recommendations.metadata.get("org_recommendations", {}); cluster_data = {"nodes": [], "links": []}; integration_data, technical_data = [], []
            return render_template( "dashboard.html", report_data=asdict(recommendations), org_funnel_data=org_funnel_data, cluster_data=cluster_data, integration_data=integration_data, technical_data=technical_data )
        except Exception as e: logger.exception(f"Error rendering dashboard: {e}"); return render_template("error.html", error=str(e), report_data={"metadata": {"generated_at": datetime.now().isoformat()},"summary": {"total_owners": 0, "total_apps": 0, "total_servers": 0}})

    @bp.route("/api/recommendations")
    def get_recommendations():
        try:
            data_file = "clustered_data_L.csv";
            if not os.path.exists(data_file): data_file = "clustered_data.csv"; logger.warning(f"L-level data file not found, falling back to {data_file}")
            app_owner_filter = request.args.get("app_owner")
            frequency = request.args.get("frequency")
            app_name = request.args.get("app")
            product_filter = request.args.get("product")
            sort_by = request.args.get("sort_by", "priority")
            sort_order = request.args.get("sort_order", "desc")

            l_level_filters = {}
            expected_l_cols = recommendation_service.l_level_columns
            for col in expected_l_cols:
                if col in request.args and request.args[col]:
                    l_level_filters[col] = request.args[col]

            logger.info(f"API request for recommendations with filters: owner='{app_owner_filter}', freq='{frequency}', app='{app_name}', product='{product_filter}', l_levels='{l_level_filters}'")
            report = recommendation_service.generate_recommendations(
                data_file=data_file,
                app_owner_filter=app_owner_filter,
                frequency_filter=frequency,
                app_name_filter=app_name,
                product_filter=product_filter,
                l_level_filters=l_level_filters if l_level_filters else None,
                sort_by=sort_by,
                sort_order=sort_order
            )
            # Get product-level recommendations with intelligent insights
            try:
                product_recommendations = recommendation_service.get_product_level_recommendations(
                    app_filter=app_name,
                    product_filter=product_filter,
                    urgency_filter=None,
                    l_levels=l_level_filters if l_level_filters else None
                )
                logger.info(f"Generated {len(product_recommendations.intelligent_recommendations)} intelligent recommendations")
            except Exception as e:
                logger.warning(f"Failed to generate product recommendations: {e}")
                product_recommendations = None

            response_data = asdict(report)
            if 'generated_at' in response_data.get('metadata', {}):
                response_data['metadata']['generated_at'] = str(response_data['metadata']['generated_at'])

            # Add product recommendations to response
            if product_recommendations:
                response_data['product_recommendations'] = asdict(product_recommendations)

            return jsonify(response_data)
        except FileNotFoundError as e: logger.error(f"Data file not found for API: {e}"); return jsonify({"error": str(e)}), 404
        except ValueError as e: logger.warning(f"Value error processing recommendation request: {e}"); return jsonify({"error": str(e)}), 400
        except Exception as e: logger.exception(f"Error getting recommendations via API: {e}"); return jsonify({"error": f"An internal server error occurred."}), 500

    @bp.route("/api/filters")
    def get_filter_options():
        try:
            data_file = "clustered_data_L.csv";
            if not os.path.exists(data_file): data_file = "clustered_data.csv"; logger.warning(f"L-level data file not found for filters, falling back to {data_file}")
            df = pd.read_csv(data_file)
            app_names = sorted(list(df["APP_NAME"].dropna().unique()))
            owners = sorted(list(df["APPOWNER"].dropna().unique()))
            products = sorted(list(df["PRODUCT"].dropna().unique()))

            l_levels_options = []
            l_columns_for_filter = ["CMDB_L4_NAME", "CMDB_L5_NAME"]
            for col in l_columns_for_filter:
                if col in df.columns:
                    unique_values = sorted(list(df[col].dropna().astype(str).unique()))
                    if unique_values:
                        l_levels_options.append({"column": col, "values": unique_values})

            return jsonify({
                "app_names": app_names,
                "owner_names": owners,
                "products": products,
                "urgency_levels": ["high", "medium", "low"],
                "l_levels": l_levels_options
            })
        except Exception as e: logger.exception(f"Error getting filter options: {e}"); return jsonify({"error": f"Could not load filter options: {e}"}), 500

    @bp.route("/api/product-recommendations")
    def get_product_recommendations():
        """API endpoint for product-level recommendations with filtering."""
        try:
            data_file = "clustered_data_L.csv"
            if not os.path.exists(data_file):
                data_file = "clustered_data.csv"
                logger.warning(f"L-level data file not found, falling back to {data_file}")

            # Get filter parameters
            app_owner_filter = request.args.get("app_owner")
            product_filter = request.args.get("product")
            confidence_filter = request.args.get("confidence", type=float)
            recommendation_type = request.args.get("type", "all")  # all, product_to_product, owner_affinity, cross_product

            # Load and filter data
            df = pd.read_csv(data_file)
            df["priority_score"] = df.apply(recommendation_service._calculate_priority_score, axis=1)

            # Apply filters
            filtered_df = df.copy()
            if app_owner_filter:
                filtered_df = filtered_df[filtered_df["APPOWNER"].astype(str).str.contains(app_owner_filter, case=False, na=False)]
            if product_filter:
                products = [product.strip() for product in product_filter.split(',')]
                filtered_df = filtered_df[filtered_df["PRODUCT"].astype(str).isin(products)]

            # Generate product-level recommendations using optimized caching
            filters_dict = {
                "app_owner": app_owner_filter,
                "product": product_filter,
                "confidence": confidence_filter,
                "type": recommendation_type
            }
            product_level_recs = recommendation_service._get_cached_or_compute_recommendations(filtered_df, filters_dict)

            # Apply confidence filter if specified
            if confidence_filter is not None:
                if recommendation_type in ["all", "product_to_product"]:
                    product_level_recs.product_to_product = [
                        rec for rec in product_level_recs.product_to_product
                        if rec.confidence_level >= confidence_filter
                    ]
                if recommendation_type in ["all", "cross_product"]:
                    product_level_recs.cross_product_recommendations = [
                        rec for rec in product_level_recs.cross_product_recommendations
                        if rec.confidence_score >= confidence_filter
                    ]

            # Filter by recommendation type
            response_data = asdict(product_level_recs)
            if recommendation_type == "product_to_product":
                response_data = {
                    "product_to_product": response_data["product_to_product"],
                    "correlation_matrix": response_data["correlation_matrix"],
                    "insights": [insight for insight in response_data["insights"] if "correlation" in insight.lower()]
                }
            elif recommendation_type == "owner_affinity":
                response_data = {
                    "owner_product_affinities": response_data["owner_product_affinities"],
                    "insights": [insight for insight in response_data["insights"] if "owner" in insight.lower() or "expert" in insight.lower()]
                }
            elif recommendation_type == "cross_product":
                response_data = {
                    "cross_product_recommendations": response_data["cross_product_recommendations"],
                    "insights": [insight for insight in response_data["insights"] if "cross-product" in insight.lower()]
                }

            # Add metadata
            response_data["metadata"] = {
                "total_records": len(filtered_df),
                "applied_filters": {
                    "app_owner": app_owner_filter,
                    "product": product_filter,
                    "confidence": confidence_filter,
                    "type": recommendation_type
                },
                "generated_at": pd.Timestamp.now().isoformat()
            }

            return jsonify(response_data)

        except Exception as e:
            logger.exception(f"Error getting product recommendations: {e}")
            return jsonify({"error": "An internal server error occurred."}), 500

    @bp.route("/api/clusters")
    def get_cluster_data():
        """API endpoint for cluster visualization data"""
        try:
            app_owner_filter = request.args.get("app_owner", None);
            if not app_owner_filter: app_owner_filter = None # Treat empty string as no filter
            logger.info(f"Cluster data requested for app owner: '{app_owner_filter}'")
            data_file = "clustered_data_L.csv";
            if not os.path.exists(data_file): data_file = "clustered_data.csv"
            df = pd.read_csv(data_file)
            cluster_df = df
            # Implementation of similar owners feature
            if app_owner_filter:
                all_owners_df = df  # Use the full dataframe
                # Find data for the target owner
                target_owner_data = all_owners_df[all_owners_df["APPOWNER"].astype(str).str.contains(app_owner_filter, case=False, na=False)]

                if not target_owner_data.empty:
                    logger.info(f"Found target owner data with {len(target_owner_data)} rows")

                    # 1. Find target owner's L-Level
                    target_l_level_col, target_l_level_val = recommendation_service.find_owner_primary_l_level(target_owner_data)

                    if target_l_level_val:  # Only proceed if we found a valid L-level
                        logger.info(f"Target owner's primary L-level: {target_l_level_col}={target_l_level_val}")

                        # 2. Find peer owners in the same L-Level
                        peer_owners_df = all_owners_df[all_owners_df[target_l_level_col] == target_l_level_val]
                        peer_owners_count = len(peer_owners_df["APPOWNER"].unique())
                        logger.info(f"Found {peer_owners_count} peer owners in the same L-level")

                        if peer_owners_count > 1:  # Only calculate similarity if there are peers
                            # 3. Calculate similarity scores for peers
                            similarity_scores = recommendation_service.calculate_similarity(peer_owners_df, target_owner_data)

                            # 4. Select top N similar owners (excluding the target owner)
                            top_n = 3  # Number of similar owners to include
                            similar_owners = similarity_scores.nlargest(top_n).index.tolist()
                            logger.info(f"Selected {len(similar_owners)} similar owners: {similar_owners}")

                            # 5. Combine target owner and similar owners
                            # Get the exact target owner name from the data
                            target_owner_exact = target_owner_data["APPOWNER"].iloc[0]
                            selected_owners = [target_owner_exact] + similar_owners

                            # Filter dataframe to include only the selected owners
                            cluster_df = all_owners_df[all_owners_df["APPOWNER"].isin(selected_owners)]
                            logger.info(f"Final cluster data includes {len(cluster_df)} rows for {len(selected_owners)} owners")
                        else:
                            # If no peers, just use the target owner data
                            logger.info("No peers found, using only target owner data")
                            cluster_df = target_owner_data
                    else:
                        # If no L-level found, just use the target owner data
                        logger.info("No L-level found for target owner, using only target owner data")
                        cluster_df = target_owner_data
                else:
                    # No data for target owner
                    logger.warning(f"No data found for target owner: {app_owner_filter}")
                    cluster_df = pd.DataFrame()  # Empty dataframe
            else:
                # No owner filter, show all data
                cluster_df = df

            cluster_data = recommendation_service._generate_product_clusters(cluster_df)
            logger.info(f"Returning cluster data with {len(cluster_data.get('nodes', []))} nodes for filter '{app_owner_filter}'")
            return jsonify(cluster_data)
        except FileNotFoundError as e: logger.error(f"Data file not found for cluster API: {e}"); return jsonify({"error": str(e)}), 404
        except Exception as e: logger.exception(f"Error getting cluster data: {e}"); return jsonify({"error": f"An internal server error occurred."}), 500

    # --- Register Blueprint ---
    app.register_blueprint(bp)
    @app.route("/health")
    def health_check():
        return jsonify({"status": "ok"}), 200

    # Start background cache cleanup
    recommendation_service.product_cache.start_background_cache_cleanup()
    logger.info("Started background cache cleanup process")

    return app

# --- Main Execution ---
if __name__ == "__main__":
    app = create_app()
    host = os.environ.get("FLASK_RUN_HOST", "127.0.0.1"); port = int(os.environ.get("FLASK_RUN_PORT", 5000)); debug_mode = os.environ.get("FLASK_DEBUG", "false").lower() in ["true", "1", "t"]
    logger.info(f"Starting Flask app on {host}:{port} (Debug: {debug_mode})")
    app.run(debug=debug_mode, host=host, port=port)

